package com.decurret_dcp.dcjpy.bcmonitoring.application.abi;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser;
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3AbiRepository;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.ConfigurationException;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3Exception;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.util.Date;
import java.util.List;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.s3.model.CommonPrefix;
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response;
import software.amazon.awssdk.services.s3.model.S3Object;

@Service
public class DownloadAbiService {
  private final LoggingService logger;
  private final S3AbiRepository s3AbiRepository;
  private final ObjectMapper mapper = new ObjectMapper();
  private final AbiParser abiParser;
  private final BcmonitoringConfigurationProperties properties;

  public DownloadAbiService(
      LoggingService logger,
      S3AbiRepository s3AbiRepository,
      AbiParser ethereumService,
      BcmonitoringConfigurationProperties properties) {
    this.logger = logger;
    this.s3AbiRepository = s3AbiRepository;
    this.abiParser = ethereumService;
    this.properties = properties;
  }

  /**
   * Execute the ABI download and parsing process.
   *
   * @throws S3Exception if an error occurs during S3 operations
   * @throws ConfigurationException if there is an error in the configuration
   * @throws IOException if there is an error parsing ABI content
   */
  public void execute() throws S3Exception, ConfigurationException, IOException {
    // Retrieve the S3 bucket name from configuration properties
    String bucketName = properties.getAws().getS3().getBucketName();
    logger.info("downloading abi files... bucket_name={}", bucketName);

    // Check if bucket name is configured
    if (bucketName == null || bucketName.isEmpty()) {
      String errorMessage = "S3 bucket name is not configured";
      logger.error(errorMessage);
      throw new ConfigurationException(errorMessage);
    }

    // List all common prefixes (folders) in the S3 bucket
    List<CommonPrefix> commonPrefixes =
        s3AbiRepository.listCommonPrefixesObjects(bucketName, DCFConst.SLASH);
    if (commonPrefixes == null || commonPrefixes.isEmpty()) {
      String errorMessage = "Failed to list S3 CommonPrefixes objects";
      logger.error(errorMessage);
      throw new S3Exception(errorMessage);
    }

    // Iterate through each common prefix (folder)
    for (CommonPrefix prefix : commonPrefixes) {
      // Remove the trailing slash from the prefix
      String prefixWithoutTrailingSlash =
          prefix.prefix().substring(0, prefix.prefix().length() - 1);

      // List all objects within the current prefix
      ListObjectsV2Response listOutput =
          s3AbiRepository.listObjects(bucketName, prefixWithoutTrailingSlash);
      if (listOutput == null) {
        String errorMessage =
            "Failed to list S3 objects with prefix: " + prefixWithoutTrailingSlash;
        logger.error(errorMessage);
        throw new S3Exception(errorMessage);
      }

      // Iterate through each object in the current prefix
      for (S3Object obj : listOutput.contents()) {
        String objKey = obj.key();

        // Skip objects that are not direct children of the current prefix
        if (objKey.chars().filter(ch -> ch == '/').count() > 1) {
          continue;
        }

        // Get the file extension of the object
        Path path = FileSystems.getDefault().getPath(objKey);
        String extension = getFileExtension(path.getFileName().toString());

        // Skip objects that do not have a .json extension
        if (!".json".equals(extension)) {
          logger.info("This object will be skipped because the extension is not .json: {}", objKey);
          continue;
        }

        // Retrieve the object from S3
        logger.info("getting s3 abi object. bucketName={}, objKey={}", bucketName, objKey);
        InputStream inputStream = s3AbiRepository.getObject(bucketName, objKey);
        if (inputStream == null) {
          String errorMessage = "Failed to get S3 abi object: " + objKey;
          logger.error(errorMessage);
          throw new S3Exception(errorMessage);
        }

        // Parse the ABI content of the object
        try {
          abiParser.parseAbiContent(inputStream, objKey, Date.from(obj.lastModified()));
        } catch (IOException e) {
          String errorMessage = "Failed to parse S3 abi object: " + objKey;
          logger.error(errorMessage, e);
          throw new IOException(errorMessage, e);
        }
      }
    }
  }

  private String getFileExtension(String filename) {
    int lastDotIndex = filename.lastIndexOf(DCFConst.DOT);
    return lastDotIndex > 0 ? filename.substring(lastDotIndex) : DCFConst.EMPTY;
  }
}
