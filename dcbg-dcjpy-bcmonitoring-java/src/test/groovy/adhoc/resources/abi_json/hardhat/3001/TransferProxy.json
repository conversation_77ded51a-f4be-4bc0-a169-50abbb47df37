{"address": "0xcc3723E2Ec24b7D0256A9DAb77895eD5222a97Af", "abi": [{"type": "event", "anonymous": false, "name": "AddRule", "inputs": [{"type": "address", "name": "rule", "indexed": false}, {"type": "uint256", "name": "position", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "DeleteRule", "inputs": [{"type": "address", "name": "rule", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "addRule", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "rule"}, {"type": "uint256", "name": "position"}], "outputs": []}, {"type": "function", "name": "clearRule", "constant": false, "payable": false, "inputs": [], "outputs": []}, {"type": "function", "name": "customTransfer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "sendAccountId"}, {"type": "bytes32", "name": "fromAccountId"}, {"type": "bytes32", "name": "toAccountId"}, {"type": "uint256", "name": "amount"}, {"type": "bytes32", "name": "miscValue1"}, {"type": "string", "name": "miscValue2"}, {"type": "string", "name": "memo"}, {"type": "bytes32", "name": "traceId"}], "outputs": []}, {"type": "function", "name": "deleteRule", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "rule"}], "outputs": []}, {"type": "function", "name": "findAll", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address[]", "name": "rules"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}, {"type": "address", "name": "token"}], "outputs": []}, {"type": "function", "name": "isRegistered", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "address", "name": "rule"}], "outputs": [{"type": "bool", "name": "result"}]}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x9c553557f270df221dd3e465844b93ea9f7f59a43ddc641275326916c74a7d05", "receipt": {"to": null, "from": "0x81B1425696634636B8d90141452f9491C92797E7", "contractAddress": "0xcc3723E2Ec24b7D0256A9DAb77895eD5222a97Af", "transactionIndex": 0, "gasUsed": "1234482", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xb707dafc297ca76f44241620489d15e396be2c83a7f4d7222e149b8993f8bafb", "blockNumber": 666, "cumulativeGasUsed": "1234482", "status": 1}}