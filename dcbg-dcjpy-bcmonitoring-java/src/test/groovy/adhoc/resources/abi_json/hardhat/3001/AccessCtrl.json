{"address": "0x5EC5FCC4f985f719CDEC3B56a76Bbf0944993A72", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "event", "anonymous": false, "name": "RoleAdminChanged", "inputs": [{"type": "bytes32", "name": "role", "indexed": true}, {"type": "bytes32", "name": "previousAdminRole", "indexed": true}, {"type": "bytes32", "name": "newAdminRole", "indexed": true}]}, {"type": "event", "anonymous": false, "name": "RoleGranted", "inputs": [{"type": "bytes32", "name": "role", "indexed": true}, {"type": "address", "name": "account", "indexed": true}, {"type": "address", "name": "sender", "indexed": true}]}, {"type": "event", "anonymous": false, "name": "RoleRevoked", "inputs": [{"type": "bytes32", "name": "role", "indexed": true}, {"type": "address", "name": "account", "indexed": true}, {"type": "address", "name": "sender", "indexed": true}]}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "ROLE_PREFIX_ACCOUNT", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "ROLE_PREFIX_ISSUER", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "ROLE_PREFIX_PROV", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "ROLE_PREFIX_VALIDATOR", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "addAccountEoa", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "address", "name": "eoa"}], "outputs": []}, {"type": "function", "name": "addAdminRole", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "eoaNew"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "role"}, {"type": "address", "name": "eoaNew"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "addRoleByIssuer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "address", "name": "eoaNew"}], "outputs": []}, {"type": "function", "name": "addRoleByProv", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}, {"type": "bytes32", "name": "role"}, {"type": "address", "name": "eoaNew"}], "outputs": []}, {"type": "function", "name": "addRoleByValidator", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "role"}, {"type": "address", "name": "eoaNew"}], "outputs": []}, {"type": "function", "name": "calcRole", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [{"type": "bytes32", "name": "prefix"}, {"type": "bytes32", "name": "id"}], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "checkAdminRole", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "hash"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "has"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkRole", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "hash"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "bool", "name": "has"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "checkSigAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "bytes32", "name": "hashAccount"}, {"type": "bytes", "name": "accountSignature"}, {"type": "bytes", "name": "info"}], "outputs": [{"type": "bool", "name": "has"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "compress", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [{"type": "uint256[2]", "name": "P"}], "outputs": [{"type": "uint8", "name": "yBit"}, {"type": "uint256", "name": "x"}]}, {"type": "function", "name": "decompress", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [{"type": "uint8", "name": "yBit"}, {"type": "uint256", "name": "Px"}], "outputs": [{"type": "uint256[2]", "name": ""}]}, {"type": "function", "name": "delAdminRole", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "eoaDel"}], "outputs": []}, {"type": "function", "name": "delIssuerRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "issuerID"}, {"type": "address", "name": "eoaDel"}], "outputs": []}, {"type": "function", "name": "delProviderRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "provID"}, {"type": "address", "name": "eoaDel"}], "outputs": []}, {"type": "function", "name": "delValidRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "validID"}, {"type": "address", "name": "eoaDel"}], "outputs": []}, {"type": "function", "name": "eccAddRecover", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "pox"}, {"type": "uint256", "name": "pkoSign"}, {"type": "uint256", "name": "pcx"}, {"type": "uint256", "name": "pkcSign"}], "outputs": [{"type": "address", "name": "addr"}]}, {"type": "function", "name": "getAccountEoa", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "getIssuerEoa", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "issuerId"}], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "getProviderEoa", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "providerId"}], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "getRoleAdmin", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "role"}], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "getValidatorEoa", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "validatorId"}], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "grantRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "role"}, {"type": "address", "name": "account"}], "outputs": []}, {"type": "function", "name": "hasRole", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes32", "name": "role"}, {"type": "address", "name": "account"}], "outputs": [{"type": "bool", "name": ""}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}, {"type": "address", "name": "eoaAdmin"}], "outputs": []}, {"type": "function", "name": "isPub<PERSON>ey", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [{"type": "uint256[2]", "name": "P"}], "outputs": [{"type": "bool", "name": ""}]}, {"type": "function", "name": "onCurve", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [{"type": "uint256[2]", "name": "P"}], "outputs": [{"type": "bool", "name": ""}]}, {"type": "function", "name": "renounceRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "role"}, {"type": "address", "name": "account"}], "outputs": []}, {"type": "function", "name": "revokeRole", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "role"}, {"type": "address", "name": "account"}], "outputs": []}, {"type": "function", "name": "supportsInterface", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "bytes4", "name": "interfaceId"}], "outputs": [{"type": "bool", "name": ""}]}, {"type": "function", "name": "validateSignature", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [{"type": "bytes32", "name": "h"}, {"type": "uint256[2]", "name": "rs"}, {"type": "uint256[2]", "name": "Q"}], "outputs": [{"type": "bool", "name": ""}]}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0xc4c3c995f70722671928220892be847dea28d70a8d687f7f50d9f76ca491b73d", "receipt": {"to": null, "from": "0x81B1425696634636B8d90141452f9491C92797E7", "contractAddress": "0x5EC5FCC4f985f719CDEC3B56a76Bbf0944993A72", "transactionIndex": 0, "gasUsed": "4802358", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x8cc6350d9df0faf541c38bfe55c8b2b84da026973e5c38554ccc7fdf32e69d38", "blockNumber": 619, "cumulativeGasUsed": "4802358", "status": 1}}